<?php

namespace SSO;

use App\Users;
use Exception;
use App\Model\v2\Roles;
use App\Model\v2\Student;
use App\Model\UserRoleType;
use Illuminate\Support\Str;
use SSO\DTO\KeycloakUserInfo;
use Support\Services\JwtToken;
use Illuminate\Foundation\Auth\User;
use Illuminate\Support\Facades\Http;
use SSO\Services\CertificateService;
use SSO\Services\ConfigurationService;
use App\Exceptions\ApplicationException;
use SSO\DTO\MetaKey;

class SSO
{


  public function prefix()
  {
    return SSOServiceProvider::PREFIX;
  }


  public function updateSSORedirectUrl($url = null)
  {


    if ($url && !validate_url($url)) {
      abort(404, 'Redirect Url not found.');
    }
    config([SSO::prefix() . '.config.redirect_url' => $url ? $url : route('galaxy.sso.callback')]);
  }

  public function oauthUrl($path)
  {
    return config('galaxysso.config.id_provider_url') . '/realms/' . config('galaxysso.config.id_provider_realm') . '/protocol/openid-connect/' . ltrim($path, '/');
  }
  public function adminUrl($path)
  {
    return config('galaxysso.config.id_provider_url') . '/admin/realms/' . config('galaxysso.config.id_provider_realm') . '/' . ltrim($path, '/');
  }

  public function authorizeUrl($params)
  {
    return $this->oauthUrl('/auth') . '?' . http_build_query($params);
  }

  public function tokenUrl()
  {
    return $this->oauthUrl('/token');
  }

  public function userInfoUrl()
  {
    return $this->oauthUrl('/userinfo');
  }

  public function saveTempLoginToken(User $user)
  {
    $token = Str::random(16) . time() . Str::random(16);
    $user->{config('galaxysso.config.login_token_column')} = $token;
    $user->{config('galaxysso.config.login_token_column') . '_expires_at'} = now()->addMinutes(5);
    // $user->updateJsonField([
    //   config('galaxysso.config.login_token_column').'_expires_at' => now()->addMinutes(5)
    // ]);
    $user->save();
    return $token;
  }

  public function findUserFromLoginToken($token)
  {
    return  app(config('galaxysso.config.auth_model'))
      ->where(config('galaxysso.config.login_token_column'), $token)
      /* STOP USERS FROM LOGGIN IN WITH EXPIRED TOKEN */
      ->whereRaw(config('galaxysso.config.login_token_column') . "_expires_at > '" . now() . "'")
      ->first();
  }

  public function logoutUserSession($userId){
    if(!$userId){
      return;
    }
    try {
      // dd($this->adminUrl('users/'.$userId.'/logout'));
      return Http::withHeaders([
        'Authorization' => 'Bearer ' . SSO::getAdminToken(),
      ])/* ->asForm() */->post($this->adminUrl('users/'.$userId.'/logout'))->json();
    }catch(\Exception $e){
      info('sso user session logout from id provider failed', [$e->getMessage()]);

    }
  }

  public function logout($token, $data = [])
  {
    try {
      info('logging out from provider');
      return Http::withHeaders([
        'Authorization' => 'Bearer ' . $token,
        'Content-Type' => 'application/x-www-form-urlencoded'
      ])/* ->asForm() */->post($this->oauthUrl('/logout'), $data)->json();


    } catch (\Exception $e) {
      info('sso logout from id provider failed', [$e->getMessage()]);
    }
    // dd($response->json());
  }

  public function clearGalaxyLoginSession(){
    session()->forget(config('galaxysso.config.access_token_session_key'));
    // session()->forget(KeycloakUserInfo::SSO_CONNECTED_KEY);

  }

  public function getAccessTokenFromSession()
  {
    return @session()->get(config('galaxysso.config.access_token_session_key'))['access_token'];
  }
  public function getRefreshTokenFromSession()
  {
    return @session()->get(config('galaxysso.config.access_token_session_key'))['refresh_token'];
  }
  public function getIdTokenFromSession()
  {
    return @session()->get(config('galaxysso.config.access_token_session_key'))['id_token'];
  }

  public function sessionLogoutUrl()
  {
    // dd(session()->get(config('galaxysso.config.access_token_session_key')));
    $params = [];
    if ($token = $this->getIdTokenFromSession()) {
      $params = [
        'id_token_hint' => $token,
        'post_logout_redirect_uri' => url('/'),
      ];
    }

    return $this->oauthUrl('/logout') . '?' . http_build_query($params);
  }


  public function getAdminToken()
  {
    try {

      $response = Http::asForm()->post($this->tokenUrl(), [
        'client_id' => 'admin-cli',
        'grant_type' => 'password',
        'username' => config('galaxysso.config.id_provider_admin.username'),
        'password' => config('galaxysso.config.id_provider_admin.password'),
      ]);

      $result = $response->json();
      if (isset($result['access_token'])) {
        return $result['access_token'];
      }
      // dd($result);

      throw new Exception("Cannot get admin access token.");
    } catch (Exception $e) {
      throw new ApplicationException($e->getMessage());
    }
  }


  public function decodeJwtToken($token)
  {
    // dd(ConfigurationService::getPublicKey());
    return JwtToken::decode($token, ConfigurationService::getPublicKey());
  }

  public function getJwksUrl()
  {
    return config('galaxysso.config.id_provider_url') . '/realms/' . config('galaxysso.config.id_provider_realm') . '/protocol/openid-connect/certs';
  }
  public function wellKnownConfigurationUrl()
  {
    return config('galaxysso.config.id_provider_url') . '/realms/' . config('galaxysso.config.id_provider_realm') . '/.well-known/openid-configuration';
  }

  public function restApiUrl($path)
  {
    // dd(config('galaxysso.config.id_provider_rest_api_url') . '/' . ltrim($path, '/'));
    return config('galaxysso.config.id_provider_url') . '/admin/realms/' . config('galaxysso.config.id_provider_realm') . '/' . ltrim($path, '/');
  }

  public function view($view)
  {
    return $this->prefix() . '::' . $view;
  }

  public function extractStudentInfoFromEmail($email, $patterns)
    {
        // $patterns = tenant()->getMeta('sso_email_patterns', $tenantId);

        // Define available placeholders and their regex
        $placeholders = [
            '{STUDENT_ID}' => '([a-zA-Z0-9_-]+)',  // Allows letters, numbers, underscores, hyphens
            '{FIRST_NAME}' => '([a-zA-Z]+)',       // Only letters
            '{LAST_NAME}' => '([a-zA-Z]+)',        // Only letters
            '{BIRTH_YEAR}' => '(\d{4})',           // 4-digit year
            '{COLLEGE_DOMAIN}' => '([a-zA-Z0-9.-]+)' // Any valid domain part
        ];

        foreach ($patterns as $pattern) {
            // Convert pattern to regex
            $regex = str_replace(array_keys($placeholders), array_values($placeholders), $pattern);
            $regex = "/^" . str_replace('@', '\@', $regex) . "$/";

            // Match the email
            if (preg_match($regex, $email, $matches)) {
                // Extract the values dynamically
                $studentInfo = [];
                $index = 1;
                foreach ($placeholders as $key => $value) {
                    if (strpos($pattern, $key) !== false) {
                        $studentInfo[str_replace(['{', '}'], '', $key)] = $matches[$index++] ?? null;
                    }
                }

                return $studentInfo; // Returns an array with extracted values
            }
        }

        return null;
    }


    public function linkStudent(Student $student, $email){
      $user = Users::where('username', $student->generated_stud_id)->orWhere('email', $email)->first();
        if ($user) {
            $oldEmail = $user->email;
            $user->email = $email;
            $user->username = $student->generated_stud_id;
        } else {
            $oldEmail = $student->email;
            $user = new Users([
                'college_id' => $student->college_id,
                'username' => $student->generated_stud_id,
                'name' =>  $student->first_name . " " . $student->family_name,
                'email' => $email,
                'phone' => $student->phone,
                'mobile' => $student->mobile,
                'security_question' => '',
                'security_answer' => '',

            ]);
            $password = bcrypt(Str::random(8));
            $user->password = $password;
        }
        /* in case we need to send galaxy form login credentials in the email */
        $user->role_id = Roles::TYPE_STUDENT;
        $user->email_verified_at = now();
        $user->status = 1;
        /* TODO: make necessary changes so this user won't see password change modal on first login. */
        $user->save();
        $user->setMeta(MetaKey::SSO_OLD_EMAIL, $oldEmail);

        /* TODO: FUTURE- I don't know why this is needed and calling model as a service is so BAD. */
        $objUserRoleType = new UserRoleType();
        $objUserRoleType->saveStudentRoleType(Roles::TYPE_STUDENT, $user->id);

        return $user;
    }
}
