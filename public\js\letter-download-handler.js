/**
 * Letter Download Handler
 * Handles click events for downloading and deleting letter files
 */

/**
 * Generate download URL using Laravel route
 */
function generateDownloadUrl(filePath) {
    // Base route URL - this should be set from Laravel
    var baseUrl = window.downloadRouteUrl || '/api/download-file-and-delete';

    // Add file path as query parameter
    return baseUrl + '?filePath=' + encodeURIComponent(filePath);
}

$(document).ready(function() {

    /**
     * <PERSON>le click on download zip link
     * This will directly download the file and delete it from storage
     */
    $('body').on('click', '#downloadZipLink', function(e) {
        e.preventDefault();

        var filePath = $(this).data('file-path') || $(this).data('filepath');

        if (!filePath) {
            console.error('No file path found');
            if (typeof showToster === 'function') {
                showToster('error', 'File path not found', '');
            } else {
                alert('Error: File path not found');
            }
            return;
        }

        // Show loading state
        var originalText = $(this).html();
        $(this).html('<i class="fa fa-spinner fa-spin"></i> Downloading...').prop('disabled', true);

        // Generate the download URL using the route
        var downloadUrl = generateDownloadUrl(filePath);

        // Trigger download - this will automatically delete the file after download
        window.location.href = downloadUrl;

        // Reset button state after a short delay
        setTimeout(function() {
            $('#downloadZipLink').html(originalText).prop('disabled', false);
        }, 2000);

        // Show success message
        if (typeof showToster === 'function') {
            showToster('success', 'Download started. File will be deleted after download.', '');
        }
    });
    
    /**
     * Handle click on any element with class 'download-letter-file'
     */
    $('body').on('click', '.download-letter-file', function(e) {
        e.preventDefault();

        var filePath = $(this).data('file-path') || $(this).data('filepath');
        var fileName = $(this).data('filename') || 'letter';

        if (!filePath) {
            console.error('No file path found');
            if (typeof showToster === 'function') {
                showToster('error', 'File path not found', '');
            } else {
                alert('Error: File path not found');
            }
            return;
        }

        // Show loading state
        var $button = $(this);
        var originalText = $button.html();
        $button.html('<i class="fa fa-spinner fa-spin"></i> Downloading...').prop('disabled', true);

        // Generate the download URL using the route
        var downloadUrl = generateDownloadUrl(filePath);

        // Trigger download
        window.location.href = downloadUrl;

        // Reset button state after a short delay
        setTimeout(function() {
            $button.html(originalText).prop('disabled', false);
        }, 2000);

        // Show success message
        if (typeof showToster === 'function') {
            showToster('success', fileName + ' download started. File will be deleted after download.', '');
        }
    });
    
    /**
     * Handle letter generation response and create download link
     */
    function handleLetterGenerationResponse(response) {
        if (response.status === 'success' && response.data) {
            // Check if it's an error response
            if (response.data.error) {
                if (typeof showToster === 'function') {
                    showToster('error', response.data.fail_msg || 'Failed to generate letter', '');
                } else {
                    alert('Error: ' + (response.data.fail_msg || 'Failed to generate letter'));
                }
                return;
            }

            // Show success message
            if (typeof showToster === 'function') {
                showToster('success', response.data.success_msg || 'Letter generated successfully', '');
            }

            // Create download link if file is available
            if (response.data.file_name && response.data.download_url) {
                createDownloadLink(response.data);
            }
        } else {
            if (typeof showToster === 'function') {
                showToster('error', 'Failed to generate letter', '');
            } else {
                alert('Error: Failed to generate letter');
            }
        }
    }
    
    /**
     * Create download link element
     */
    function createDownloadLink(data) {
        var fileName = data.file_name.split('/').pop(); // Get filename from path
        var fileType = fileName.includes('.zip') ? 'ZIP' : 'PDF';
        var icon = fileType === 'ZIP' ? 'fa-file-archive' : 'fa-file-pdf';

        var downloadHtml = `
            <div class="download-container mt-3 p-3 border rounded bg-light">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h6 class="mb-1">
                            <i class="fas ${icon} text-primary"></i>
                            ${data.success_msg}
                        </h6>
                        <small class="text-muted">
                            File: ${fileName} | Type: ${fileType}
                        </small>
                    </div>
                    <div class="col-md-4 text-right">
                        <a href="#"
                           id="downloadZipLink"
                           class="btn btn-success btn-sm download-letter-file"
                           data-file-path="${data.file_name}"
                           data-filename="${fileName}">
                            <i class="fas fa-download"></i> Download ${fileType}
                        </a>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-info">
                        <i class="fas fa-info-circle"></i>
                        Note: File will be automatically deleted after download for security.
                    </small>
                </div>
            </div>
        `;

        // Remove any existing download containers
        $('.download-container').remove();

        // Add download link to the page (you can customize where to append this)
        if ($('#letterGenerationResult').length) {
            $('#letterGenerationResult').html(downloadHtml);
        } else if ($('.letter-generation-form').length) {
            $('.letter-generation-form').after(downloadHtml);
        } else {
            // Fallback: append to body or a common container
            $('body').append('<div id="letterGenerationResult">' + downloadHtml + '</div>');
        }
    }
    
    // Make the function globally available
    window.handleLetterGenerationResponse = handleLetterGenerationResponse;
    window.createDownloadLink = createDownloadLink;
});

/**
 * Example usage for AJAX letter generation:
 * 
 * $.ajax({
 *     url: '/api/student-issue-letter-generate',
 *     method: 'POST',
 *     data: {
 *         student_id: studentId,
 *         course_id: courseId,
 *         letter_template_id: templateId,
 *         letter_content: letterContent
 *     },
 *     success: function(response) {
 *         handleLetterGenerationResponse(response);
 *     },
 *     error: function(xhr, status, error) {
 *         if (typeof showToster === 'function') {
 *             showToster('error', 'Failed to generate letter', '');
 *         } else {
 *             alert('Error: Failed to generate letter');
 *         }
 *     }
 * });
 */
