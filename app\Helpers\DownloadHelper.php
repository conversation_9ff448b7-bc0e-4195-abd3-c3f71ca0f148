<?php

namespace App\Helpers;

class DownloadHelper
{
    /**
     * Generate download link HTML for letter files
     * 
     * @param string $filePath The file path relative to public directory
     * @param string $buttonText The text to display on the button
     * @param string $buttonClass CSS classes for the button
     * @param string $fileName Optional filename for display
     * @return string HTML for the download link
     */
    public static function generateDownloadLink($filePath, $buttonText = 'Download', $buttonClass = 'btn btn-success', $fileName = null)
    {
        if (!$fileName) {
            $fileName = basename($filePath);
        }
        
        $fileType = pathinfo($fileName, PATHINFO_EXTENSION);
        $icon = strtolower($fileType) === 'zip' ? 'fa-file-archive' : 'fa-file-pdf';
        
        return sprintf(
            '<a href="#" id="downloadZipLink" class="%s download-letter-file" data-file-path="%s" data-filename="%s">
                <i class="fas %s"></i> %s
            </a>',
            $buttonClass,
            htmlspecialchars($filePath),
            htmlspecialchars($fileName),
            $icon,
            htmlspecialchars($buttonText)
        );
    }
    
    /**
     * Generate download container HTML with file info
     * 
     * @param string $filePath The file path relative to public directory
     * @param string $successMessage Success message to display
     * @param string $fileName Optional filename for display
     * @return string HTML for the download container
     */
    public static function generateDownloadContainer($filePath, $successMessage, $fileName = null)
    {
        if (!$fileName) {
            $fileName = basename($filePath);
        }
        
        $fileType = pathinfo($fileName, PATHINFO_EXTENSION);
        $fileTypeUpper = strtoupper($fileType);
        $icon = strtolower($fileType) === 'zip' ? 'fa-file-archive' : 'fa-file-pdf';
        
        return sprintf(
            '<div class="download-container mt-3 p-3 border rounded bg-light">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h6 class="mb-1">
                            <i class="fas %s text-primary"></i> 
                            %s
                        </h6>
                        <small class="text-muted">
                            File: %s | Type: %s
                        </small>
                    </div>
                    <div class="col-md-4 text-right">
                        <a href="#" 
                           id="downloadZipLink" 
                           class="btn btn-success btn-sm download-letter-file"
                           data-file-path="%s"
                           data-filename="%s">
                            <i class="fas fa-download"></i> Download %s
                        </a>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-info">
                        <i class="fas fa-info-circle"></i> 
                        Note: File will be automatically deleted after download for security.
                    </small>
                </div>
            </div>',
            $icon,
            htmlspecialchars($successMessage),
            htmlspecialchars($fileName),
            $fileTypeUpper,
            htmlspecialchars($filePath),
            htmlspecialchars($fileName),
            $fileTypeUpper
        );
    }
    
    /**
     * Get the download route URL with file path
     * 
     * @param string $filePath The file path relative to public directory
     * @return string The complete download URL
     */
    public static function getDownloadUrl($filePath)
    {
        return route('download-file-and-delete', ['filePath' => $filePath]);
    }
}
