<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Letter Download Example</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4><i class="fas fa-download"></i> Letter Download Example</h4>
                    </div>
                    <div class="card-body">
                        <p>This example demonstrates the download functionality with automatic file deletion.</p>
                        
                        <!-- Example download links -->
                        <div class="mb-4">
                            <h6>Example Download Links (No href, using data attributes):</h6>

                            <!-- Single PDF Download -->
                            <div class="mb-2">
                                <a href="#"
                                   id="downloadZipLink"
                                   class="btn btn-primary download-letter-file"
                                   data-file-path="uploads/student_letters/sample_letter.pdf"
                                   data-filename="sample_letter.pdf">
                                    <i class="fas fa-file-pdf"></i> Download Single PDF
                                </a>
                                <small class="text-muted ms-2">Single student letter (PDF)</small>
                            </div>

                            <!-- ZIP Download -->
                            <div class="mb-2">
                                <a href="#"
                                   class="btn btn-success download-letter-file"
                                   data-file-path="uploads/student_letters_zip/bulk_letters.zip"
                                   data-filename="bulk_letters.zip">
                                    <i class="fas fa-file-archive"></i> Download ZIP File
                                </a>
                                <small class="text-muted ms-2">Multiple student letters (ZIP)</small>
                            </div>
                        </div>
                        
                        <!-- Test buttons for different scenarios -->
                        <div class="mb-4">
                            <h6>Test Different Scenarios:</h6>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary" onclick="simulateSingleStudent()">
                                    <i class="fas fa-user"></i> Simulate Single Student
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="simulateMultipleStudents()">
                                    <i class="fas fa-users"></i> Simulate Multiple Students
                                </button>
                                <button type="button" class="btn btn-outline-danger" onclick="simulateError()">
                                    <i class="fas fa-exclamation-triangle"></i> Simulate Error
                                </button>
                            </div>
                        </div>
                        
                        <!-- Result container -->
                        <div id="letterGenerationResult"></div>
                    </div>
                </div>
                
                <!-- Instructions -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle"></i> How It Works</h5>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li><strong>Generate Letter:</strong> When you generate a letter, the API returns a download URL</li>
                            <li><strong>Click Download:</strong> Click on the download link (with ID <code>#downloadZipLink</code> or class <code>.download-letter-file</code>)</li>
                            <li><strong>Automatic Download:</strong> The file downloads immediately</li>
                            <li><strong>Automatic Deletion:</strong> The file is automatically deleted from the server after download</li>
                        </ol>
                        
                        <div class="alert alert-info mt-3">
                            <strong>Route:</strong> <code>{{ route('download-file-and-delete') }}?filePath={file_path}</code><br>
                            <strong>Controller:</strong> <code>StudentApiController@downloadFileAndDelete</code><br>
                            <strong>Feature:</strong> Uses Laravel's <code>deleteFileAfterSend(true)</code><br>
                            <strong>HTML:</strong> <code>&lt;a href="#" id="downloadZipLink" data-file-path="path/to/file.pdf"&gt;</code>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Our custom download handler -->
    <script src="{{ asset('js/letter-download-handler.js') }}"></script>

    <script>
        // Set the Laravel route URL for JavaScript to use
        window.downloadRouteUrl = '{{ route("download-file-and-delete") }}';
    </script>

    <script>
        // Mock showToster function
        function showToster(type, message, title) {
            const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            const icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';
            
            const toast = $(`
                <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
                     style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                    <i class="fas ${icon}"></i> ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `);
            
            $('body').append(toast);
            
            setTimeout(() => {
                toast.alert('close');
            }, 5000);
        }

        // Simulation functions
        function simulateSingleStudent() {
            const mockResponse = {
                status: 'success',
                data: {
                    success_msg: 'Single student letter generated successfully.',
                    file_name: 'uploads/student_letters/Letter_STUD001.pdf'
                }
            };

            handleLetterGenerationResponse(mockResponse);
        }

        function simulateMultipleStudents() {
            const mockResponse = {
                status: 'success',
                data: {
                    success_msg: '3 students letters generated successfully.',
                    file_name: 'uploads/student_letters_zip/StudentLetters_20231214123456.zip'
                }
            };

            handleLetterGenerationResponse(mockResponse);
        }

        function simulateError() {
            const mockResponse = {
                status: 'success',
                data: {
                    error: true,
                    fail_msg: 'No letters were generated successfully.'
                }
            };
            
            handleLetterGenerationResponse(mockResponse);
        }
    </script>
</body>
</html>
