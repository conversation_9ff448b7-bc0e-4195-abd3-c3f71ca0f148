<?php

namespace App\Traits;

use App;
use App\DTO\studentProfile\SendQueueLetterEmailPayload;
use App\Helpers\Helpers;
use App\Model\SendMail;
use App\Model\v2\CollegeDetails;
use App\Model\v2\LetterSetting;
use App\Model\v2\ReportLetterSetup;
use App\Model\v2\SmtpSetup;
use App\Model\v2\Student;
use App\Model\v2\StudentAttendanceWarnings;
use App\Model\v2\StudentSubjectEnrolment;
use Auth;
use Carbon\Carbon;
use File;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Str;
use Support\Services\UploadService;
use ZipArchive;

trait LetterGenerateAndSendTrait
{
    use SendEmailTrait;

    public function generateLetterPdfTraitV2($request, $watermark = false)
    {
        set_time_limit(0);
        $collegeId = Auth::user()->college_id;
        $courseId = $request->course_id ?? '';
        $studentIds = $this->getStudentIds($request->student_id);
        $studentArrInfo = $this->getStudentCoursesDetail($collegeId, $studentIds, $courseId);

        $letterSubjectTxt = $request->letter_subject ?? 'Generate Student Letter';
        $filePaths = $this->prepareFilePaths($collegeId);

        $studentData = $this->processStudentsAndGenerateLetters(
            $studentArrInfo,
            $letterSubjectTxt,
            $request,
            $courseId,
            $filePaths,
            $watermark
        );

        return $this->successResponse('Create zip file successfully', 'data', $studentData['response']);
    }

    private function getStudentIds($studentIdInput)
    {
        // Ensure student IDs are in array format
        if (is_array($studentIdInput)) {
            return $studentIdInput;
        }

        return ! empty($studentIdInput) ? explode(',', $studentIdInput) : [];
    }

    private function getStudentCoursesDetail($collegeId, $studentIds, $courseId)
    {
        // Fetch students with their course data
        return Student::with(['studentCourses' => function ($query) use ($courseId) {
            if (! empty($courseId)) {
                $query->where('course_id', $courseId);
            }
        }, 'college'])
            ->whereIn('id', $studentIds)
            ->where('college_id', $collegeId)
            ->get();
    }

    private function prepareFilePaths($collegeId)
    {
        $filePath = Config::get('constants.uploadFilePath.StudentLetterSetting');
        $subFolder = $this->getUniqueFileName('StudentLetters_');
        $destinationPath = Helpers::changeRootPath($filePath, $subFolder, $collegeId);
        File::makeDirectory($destinationPath['default'], 0777, true, true);

        return [
            'subFolder' => $subFolder,
            'destinationPath' => $destinationPath['default'],
            'destinationView' => $destinationPath['view'],
        ];
    }

    private function processStudentsAndGenerateLetters($studentArrInfo, $letterSubjectTxt, $request, $courseId, $filePaths, $watermark)
    {
        $studentData = [];
        $studentGenerateCount = 0;
        $studentNotGenerateCount = 0;

        // Prepare watermark path if flag is true
        $letterSettingData = LetterSetting::find(1);

        $watermarkPath = $this->prepareWatermarkPath($watermark, $letterSettingData->watermark);

        foreach ($studentArrInfo as $student) {
            $courseMatched = false;
            foreach ($student->studentCourses as $studCourse) {
                if (empty($courseId) || $courseId == $studCourse->course_id) {
                    $studentGenerateCount++;
                    $studentData['pdf'] = $this->generateStudentPdf($student, $courseId, $letterSubjectTxt, $request, $filePaths, $watermarkPath, $letterSettingData);
                    $courseMatched = true;
                    break;
                }
            }
            if (! $courseMatched) {
                $studentNotGenerateCount++;
                $studentData['failData'][] = $this->getFailDataArr($student, 'Course Not Found');
            }
        }

        // Add success and failure messages
        $studentData['response'] = $this->createResponseMessages(
            $studentGenerateCount,
            $studentNotGenerateCount,
            $filePaths['subFolder'],
            $filePaths,
            $studentArrInfo
        );

        return $studentData;
    }

    private function generateStudentPdf($student, $courseId, $letterSubjectTxt, $request, $filePaths, $watermarkPath, $letterSettingData)
    {
        $destinationPath = $filePaths['destinationPath'];

        // Generate letter content and subject
        [$convertedSubject, $convertedData] = $this->getLetterEmailContentWithSubject(
            $request->letter_content,
            $student->id,
            $courseId,
            $letterSubjectTxt
        );

        $pdfData = [
            'letterBody' => $convertedData,
            'template_type' => 0,
        ];

        if (! empty($watermarkPath)) {
            $objCollegeDetails = $student->college;

            $arrState = Config::get('constants.arrState');
            $collegeFilePath = Config::get('constants.uploadFilePath.CollegeLogo');
            $collegeDestinationPath = Helpers::changeRootPath($collegeFilePath);
            $clgLogoPath = $collegeDestinationPath['view'].$student->college->college_logo;

            $pdfData['clg_logo'] = $clgLogoPath;
            $pdfData['latterHead'] = $this->replaceContent($letterSettingData->header, $objCollegeDetails, $clgLogoPath, $arrState);
            $pdfData['latterFooter'] = $this->replaceContent($letterSettingData->footer, $objCollegeDetails, $clgLogoPath, $arrState);
            $pdfData['watermarkPath'] = $watermarkPath;
            $pdfData['letterSettingData'] = $letterSettingData;
            $pdfData['template_type'] = 1;
        }
        // dd($pdfData);
        // Generate and save PDF
        $pdf = App::make('dompdf.wrapper');
        $pdf->loadView('frontend.student.students-send-letter-pdf', $pdfData);
        $pdf->output();
        $dom_pdf = $pdf->getDomPDF();
        $canvas = $dom_pdf->get_canvas();
        $canvas->page_text(280, 800, 'Page {PAGE_NUM} of {PAGE_COUNT}', null, 6, [0, 0, 0]);

        // Determine letter name and save the PDF
        $letterName = ReportLetterSetup::find($request->letter_template_id)['letter_name'] ?? 'Letter';
        $fileName = str_replace('/', '_', "{$letterName}_{$student->generated_stud_id}.pdf");
        $pdf->save("{$destinationPath}{$fileName}");

        return $pdf;
    }

    private function createResponseMessages($studentGenerateCount, $studentNotGenerateCount, $subFolder, $filePaths, $studentArrInfo)
    {
        $response = [];

        // If no students generated, return error
        if ($studentGenerateCount == 0) {
            $response['error'] = true;
            $response['fail_msg'] = 'No letters were generated successfully.';

            return $response;
        }

        // If Single record, return single PDF file path
        if ($studentGenerateCount == 1) {
            $response['success_msg'] = 'Single student letter generated successfully.';
            $response['file_name'] = $this->getSinglePdfFilePath($subFolder, $filePaths, $studentArrInfo);
        } else {
            $response['success_msg'] = config_locale([
                'messages.attendance.letter_generated',
                [
                    'count' => $studentGenerateCount,
                    'label' => 'student'.($studentGenerateCount > 1 ? 's' : ''),
                ],
            ]);

            $response['file_name'] = $this->studentIssueLetterZipV2($subFolder);
        }

        if ($studentNotGenerateCount > 0) {
            $response['fail_msg'] = config_locale([
                'messages.attendance.letter_not_generated',
                [
                    'count' => $studentNotGenerateCount,
                    'label' => 'student'.($studentNotGenerateCount > 1 ? 's' : ''),
                ],
            ]);
        }

        return $response;
    }

    private function getSinglePdfFilePath($subFolder, $filePaths, $studentArrInfo)
    {
        // Get the first (and only) student from the array
        $student = $studentArrInfo->first();

        // Get the letter name from the request (we need to access it from the calling context)
        // For now, we'll scan the directory for the PDF file
        $files = File::files($filePaths['destinationPath']);

        if (count($files) > 0) {
            $pdfFile = $files[0]; // Get the first (and should be only) PDF file
            $fileName = basename($pdfFile);

            // Return the view path for the PDF file
            return $filePaths['destinationView'].$fileName;
        }

        return null;
    }

    private function prepareWatermarkPath($watermarkFlag, $watermark)
    {
        if ($watermarkFlag) {
            $filePath1 = Config::get('constants.uploadFilePath.LetterSetting');
            $destinationLetterPath = Helpers::changeRootPath($filePath1);

            return UploadService::imageEmbed($destinationLetterPath['view'].$watermark);
        }

        return null;
    }

    private function getUniqueFileName($prefix = '')
    {
        return $prefix.Str::uuid().'_'.time();
    }

    private function getExistingAttachmentIds($payloadDTO)
    {
        return ! empty($payloadDTO->letter_existing_attachment_id)
            ? explode(',', $payloadDTO->letter_existing_attachment_id)
            : [];
    }

    private function getExistingAttachments($letterExistAttachmentIdArr, $letterTemplateId, $collegeId)
    {
        $existFilePath = Config::get('constants.uploadFilePath.LetterFile');
        $existDestinationPath = Helpers::changeRootPath($existFilePath, $letterTemplateId, $collegeId);

        $docList = $this->studentProfileCommonRepository->letterTemplateDocModel($letterExistAttachmentIdArr);
        $existAttachment = [];
        $existAttachmentLogData = [];

        foreach ($docList as $doc) {
            $existAttachment[] = $existDestinationPath['view'].$doc['letter_attachment'];
            $existAttachmentLogData[$doc['letter_attachment']] = $existDestinationPath['view'].$doc['letter_attachment'];
        }

        return [$existAttachment, $existAttachmentLogData];
    }

    public function sendLetterWithMailToStudentTrait(SendQueueLetterEmailPayload $payloadDTO)
    {
        $collegeId = $payloadDTO->college_id;
        $courseId = $payloadDTO->course_id ?? '';
        $studentIds = $this->getStudentIds($payloadDTO->student_id);
        $studentArrInfo = $this->getStudentCoursesDetail($collegeId, $studentIds, $courseId);

        $letterSubjectTxt = $payloadDTO->letter_subject ?? 'Generate Student Letter';
        $filePaths = $this->prepareFilePaths($collegeId);

        $failReason = $this->checkLetterTestMail($payloadDTO);
        if (! empty($failReason)) {
            return ['success_msg' => '', 'fail_msg' => $failReason, 'statusData' => []];
        }

        $letterTemplateId = $payloadDTO->letter_template_id ?? '';
        $letterExistAttachmentIdArr = $this->getExistingAttachmentIds($payloadDTO);

        $existAttachment = [];
        $existAttachmentLogData = [];
        if (! empty($letterExistAttachmentIdArr)) {
            [$existAttachment, $existAttachmentLogData] = $this->getExistingAttachments($letterExistAttachmentIdArr, $letterTemplateId, $collegeId);
        }
        $smtpData = SmtpSetup::get()->first();
        $payload = $payloadDTO->toArray();

        $failedStudentId = [];
        $failedStudentIdWithReason = [];
        $successStudentIdWithReason = [];

        foreach ($studentArrInfo as $student) {
            $flag = false;
            $studentName = $student->first_name.' '.$student->family_name;
            foreach ($student->studentCourses as $studCourse) {
                if (empty($courseId) || $courseId == $studCourse->course_id) {

                    [$convertedSubject, $convertedData] = $this->getLetterEmailContentWithSubject($payloadDTO->letter_content, $studCourse->student_id, $studCourse->course_id, $letterSubjectTxt);

                    $pdf = App::make('dompdf.wrapper');
                    $pdfData = ['letterBody' => $convertedData, 'template_type' => 1];
                    $pdf->loadView('frontend.student.students-send-letter-pdf', $pdfData);
                    $tempFileName = time().'.pdf';
                    $tempAttachment = $filePaths['destinationPath'].$tempFileName;
                    // $pdf->save($tempAttachment);

                    $filePath = Config::get('constants.uploadFilePath.StudentLetterSetting');
                    $destinationPath = $this->changeRootPath($filePath, $collegeId);
                    $pdfContent = $pdf->output();
                    // $upload_success = UploadService::uploadAs($destinationPath['view'],$pdfContent, $tempFileName);
                    // info('file uploaded form send Letter', [$upload_success]);

                    $tmpPath = tempnam(sys_get_temp_dir(), 'pdf_');
                    file_put_contents($tmpPath, $pdfContent);
                    $upload_success = UploadService::uploadAs($destinationPath['view'], new \Illuminate\Http\File($tmpPath), $tempFileName);
                    info('file uploaded form generatePdf', [$upload_success]);
                    @unlink($tmpPath);

                    $existAttachmentLogData[$tempFileName] = $destinationPath['view'].$tempFileName;
                    $tempAttachmentNew = $destinationPath['view'].$tempFileName;

                    $mailData = $this->prepareLetterEmailData($student, $smtpData, $convertedSubject, $convertedData, $existAttachment, $tempAttachmentNew, $existAttachmentLogData);
                    $sentEmail = $this->sendLetterEmailEvent($mailData, new Request($payload), $studentName);
                    $this->logLetterEmailStatus($sentEmail, $student, $failedStudentId, $failedStudentIdWithReason, $successStudentIdWithReason);
                    $flag = true;
                    break;
                }
            }
            if (! $flag) {
                $reasonText = "Email send failed for $studentName";
                $this->logFailedStudent($student, 'Course Not Found', $reasonText, $failedStudentId, $failedStudentIdWithReason);
            }
        }

        return [
            'failed' => $failedStudentId,
            'failed_student_id_with_reason' => $failedStudentIdWithReason,
            'successStudentIdWithReason' => $successStudentIdWithReason,
        ];
    }

    private function sendLetterEmailEvent($mailData, $requestData, $recipientName)
    {
        if (empty($mailData['to'])) {
            return ['status' => 'fail', 'message' => "Email not found for $recipientName."];
        }

        $mailData['college_id'] = $requestData['college_id'];
        $mailStatus = (new SendMail)->sendSmtpMailQueue($mailData);

        if ($mailStatus['status'] == 'success') {
            if (isset($requestData['offer_comm_log']) && $requestData['offer_comm_log'] == 'on') {
                $requestData = (object) $requestData;
                $this->communicationLog->saveCommunicationLog($requestData, $mailData['id'], $mailData['body'], $mailData['to'], $mailData['from'], $mailData['attachmentLogData'], $mailData);
            }
        }

        return $mailStatus;
    }

    private function getLetterEmailContentWithSubject($content, $studentId, $courseId = '', $subject = '', $studentCourseId = '')
    {
        $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
        $destinationPath = Helpers::changeRootPath($filePath);
        if (empty($courseId)) {
            $arrStudentCourse = $this->studentProfileCommonRepository->getStudentEmailContent($studentId);
        } else {
            $arrStudentCourse = $this->studentProfileCommonRepository->getStudentCoursesEmailContent($studentId, $courseId, $studentCourseId);
        }

        $arrStudentEnrolledCourse = $this->studentProfileCommonRepository->getArrayStudentEnrolledCourseName($studentId, $studentCourseId);
        $arrStudentOfferedCourse = $this->studentProfileCommonRepository->getArrayStudentOfferedCourseName($studentId, $studentCourseId);
        $enrolledUnitList = $this->studentProfileCommonRepository->getUnitDetailList($studentId, $courseId, $studentCourseId);
        if (! empty($arrStudentCourse)) {

            if (empty($courseId)) {
                $arrStudentEnrolledUnit = [];
            } else {
                $filterArray = [
                    'student_id' => $studentId,
                    'course_id' => $courseId,
                ];
                $arrStudentEnrolledUnit = StudentSubjectEnrolment::with(['unit'])->withStudentCourseId($studentCourseId)->where($filterArray)->get()->toArray();
            }

            $row = $arrStudentCourse[0];
            $row['arrStudentEnrolledCourse'] = $arrStudentEnrolledCourse;
            $row['arrStudentOfferedCourse'] = $arrStudentOfferedCourse;
            $row['arrStudentEnrolledUnit'] = $arrStudentEnrolledUnit;
            $row['enrolledUnitList'] = $enrolledUnitList;
            $imgArr = [
                'collegeLogoUrl' => $this->getCollegeAssetUrl($destinationPath['view'], $row['college_logo']),
                'collegeSignatureUrl' => $this->getCollegeAssetUrl($destinationPath['view'], $row['college_signature']),
                'deanSignatureUrl' => $this->getCollegeAssetUrl($destinationPath['view'], $row['dean_signature']),
            ];

            $dataArr = $this->letterDataBind($row, $imgArr, $courseId);
            foreach ($dataArr as $key => $value) {
                $content = str_replace("$key", $value, $content);
                $subject = str_replace("$key", $value, $subject);
            }

            return [$subject, $content];
        } else {
            return false;
        }
    }

    private function prepareLetterEmailData($student, $smtpData, $subject, $body, $existAttachment, $tempAttachmentPath, $existAttachmentLogData)
    {
        return [
            'id' => $student->id,
            'from' => $smtpData ? $smtpData->username : '',
            'to' => $student->email ?? '',
            'cc' => '',
            'bcc' => '',
            'subject' => $subject,
            'attachFile' => array_merge($existAttachment, [$tempAttachmentPath]),
            'body' => $body,
            'attachmentLogData' => $existAttachmentLogData,
        ];
    }

    private function logLetterEmailStatus($sentEmail, $student, &$failedStudentId, &$failedStudentIdWithReason, &$successStudentIdWithReason)
    {
        if ($sentEmail['status'] == 'fail') {
            $failedStudentIdWithReason[] = [
                'type' => 'Email Not Valid',
                'reason' => $sentEmail['message'],
            ];
            $failedStudentId[] = $student->id;
        } else {
            $successStudentIdWithReason[] = $student->first_name.' '.$student->family_name;
        }
    }

    private function logFailedStudent($student, $type, $reason, &$failedStudentId, &$failedStudentIdWithReason)
    {
        $failedStudentId[] = $student->id;
        $failedStudentIdWithReason[] = [
            'type' => $type,
            'reason' => $reason,
        ];
    }

    public function generateLetterPdfTrait($request)
    {
        if (is_array($request->student_id)) {
            $studentIds = $request->student_id;
        } else {
            $studentIds = (! empty($request->student_id)) ? explode(',', $request->student_id) : [];
        }
        $courseId = $request->course_id;
        $studentCourseId = $request->student_course_id;
        if (! empty($studentCourseId)) {
            $studentArrInfo = $this->studentProfileCommonRepository->getStudentCoursesById($studentCourseId, $studentIds);
        } else {
            $studentArrInfo = $this->studentProfileCommonRepository->getStudentCoursesByCourse($courseId, $studentIds);
        }

        $studentData['data']['failData'] = [];
        $studentGenerateCount = 0;
        $studentNotGenerateCount = 0;
        $letterSubjectTxt = ($request->letter_subject != '') ? $request->letter_subject : 'Generate Student Letter';

        $filePath = Config::get('constants.uploadFilePath.StudentLetterSetting');

        $subFolder = $this->getUniqueFileName('StudentLetters_');

        $destinationPath = Helpers::changeRootPath($filePath, $subFolder);

        File::makeDirectory($destinationPath['default'], 0777, true, true);

        $previewPdf = null;

        foreach ($studentArrInfo as $studRow) {
            $flag = false;
            foreach ($studRow->studentcourses as $courseInfo) {
                // if ($courseId == $courseInfo->course_id || $studentCourseId == $courseInfo->id) {
                if ($courseId == $courseInfo->course_id) {
                    $studentGenerateCount++;

                    [$convertedSubject, $convertedData] = $this->getLetterContentWithSubject($courseInfo->student_id, $courseInfo->course_id, $request->letter_content, $letterSubjectTxt);
                    $contentData['data'] = $convertedData;
                    $contentData['subject'] = $convertedSubject;

                    $data['letterBody'] = $convertedData;
                    $data['template_type'] = $contentData['data'];
                    $pdf = App::make('dompdf.wrapper');
                    $pdf->loadView('frontend.student.students-send-letter-pdf', $data);
                    $letterName = ReportLetterSetup::find($request->letter_template_id);
                    $letterName = $letterName['letter_name'] ?? 'Letter';
                    $fileName = $letterName.'_'.$studRow->generated_stud_id.'.pdf';
                    $fileName = str_replace('/', '_', $fileName);
                    if ($request->action === 'preview') {
                        $previewPdf = $pdf;
                    } else {
                        $pdf->save($destinationPath['default'].$fileName);
                    }
                    $flag = true;
                    break;
                }
            }
            if (! $flag) {
                $studentNotGenerateCount++;
                $studentData['data']['failData'][] = $this->getFailDataArr($studRow, 'Course Not Found');
            }
        }
        if ($request->action === 'preview') {
            return $pdf;
        }
        if ($studentGenerateCount > 0) {
            $studentData['data']['success_msg'] = config_locale(
                [
                    'messages.attendance.letter_generated',
                    [
                        'count' => $studentGenerateCount,
                        'label' => 'student'.($studentGenerateCount > 1 ? 's' : ''),
                    ],
                ]);
            $studentData['data']['file_name'] = $this->studentIssueLetterZipV2($subFolder);
        }
        if ($studentNotGenerateCount > 0) {
            $studentData['data']['fail_msg'] = config_locale(
                [
                    'messages.attendance.letter_not_generated',
                    [
                        'count' => $studentNotGenerateCount,
                        'label' => 'student'.($studentNotGenerateCount > 1 ? 's' : ''),
                    ],
                ]);
        }

        return $studentData;
    }

    public function generateLetterWithWatermarkPdfTrait($request)
    {
        if (is_array($request->student_id)) {
            $studentIds = $request->student_id;
        } else {
            $studentIds = (! empty($request->student_id)) ? explode(',', $request->student_id) : [];
        }
        $courseId = $request->course_id;
        $studentCourseId = $request->student_course_id;
        if (! empty($studentCourseId)) {
            $studentArrInfo = $this->studentProfileCommonRepository->getStudentCoursesById($studentCourseId, $studentIds);
        } else {
            $studentArrInfo = $this->studentProfileCommonRepository->getStudentCoursesByCourse($courseId, $studentIds);
        }
        $studentData['data']['failData'] = [];
        $studentGenerateCount = 0;
        $studentNotGenerateCount = 0;
        $letterSubjectTxt = ($request->letter_subject != '') ? $request->letter_subject : 'Generate Student Letter';

        $filePath = Config::get('constants.uploadFilePath.StudentLetterSetting');

        $subFolder = $this->getUniqueFileName('StudentLetters_');

        $destinationPath = Helpers::changeRootPath($filePath, $subFolder);
        File::makeDirectory($destinationPath['default'], 0777, true, true);
        $letterSettingData = LetterSetting::find(1);

        $filePath1 = Config::get('constants.uploadFilePath.LetterSetting');
        $destinationLetterPath = Helpers::changeRootPath($filePath1);
        $watermarkPath = $destinationLetterPath['view'];

        foreach ($studentArrInfo as $studRow) {
            $flag = false;
            foreach ($studRow->studentcourses as $courseInfo) {
                if ($courseId == $courseInfo->course_id || $studentCourseId == $courseInfo->id) {
                    $studentGenerateCount++;
                    $objCollegeDetails = CollegeDetails::where('rto_college_details.college_id', Auth::user()->college_id)
                        ->join('rto_colleges', 'rto_colleges.id', '=', 'rto_college_details.college_id')
                        ->get();
                    [$convertedSubject, $convertedData] = $this->getLetterContentWithSubject($courseInfo->student_id, $courseInfo->course_id, $request->letter_content, $letterSubjectTxt);
                    $contentData['data'] = $convertedData;
                    $contentData['subject'] = $convertedSubject;
                    $data['letterBody'] = $convertedData;
                    $data['watermarkPath'] = $watermarkPath;
                    $data['letterSettingData'] = $letterSettingData;
                    $data['template_type'] = 1;
                    $arrState = Config::get('constants.arrState');
                    $filePath1 = Config::get('constants.uploadFilePath.CollegeLogo');
                    $destinationPath1 = Helpers::changeRootPath($filePath1);
                    $logoPath = $destinationPath1['view'];
                    $arrStudentInfo = $this->student->getStudentDetail($courseInfo->student_id, Config::get('constants.arrRoleType'));
                    $data['clg_logo'] = $clgLogo = $logoPath.$arrStudentInfo[0]->college_logo;
                    $data['latterHead'] = $this->replaceContent($letterSettingData->header, $objCollegeDetails[0], $clgLogo, $arrState);
                    $data['latterFooter'] = $this->replaceContent($letterSettingData->footer, $objCollegeDetails[0], $clgLogo, $arrState);

                    $letterName = ReportLetterSetup::find($request->letter_template_id);
                    $letterName = $letterName['letter_name'] ?? 'Letter';
                    $fileName = $letterName.'_'.$arrStudentInfo[0]->generated_stud_id.'.pdf';

                    $pdf = App::make('dompdf.wrapper');
                    $pdf->loadView('frontend.student.students-send-letter-pdf', $data);
                    $pdf->save($destinationPath['default'].$fileName);
                    $flag = true;
                    break;
                }
            }
            if (! $flag) {
                $studentNotGenerateCount++;
                $studentData['data']['failData'][] = $this->getFailDataArr($studRow, 'Course Not Found');
            }
        }
        $studentData['data']['success_count'] = $studentGenerateCount;
        $studentData['data']['success_msg'] = $studentGenerateCount.(($studentGenerateCount > 1) ? ' students were' : ' student was').' successfully generated letter.';
        $studentData['data']['fail_msg'] = ($studentNotGenerateCount > 0) ? ($studentNotGenerateCount.(($studentNotGenerateCount > 1) ? ' students were' : ' student was').' not generated letter.') : '';

        if ($studentGenerateCount > 0) {
            $studentData['data']['file_name'] = $this->studentIssueLetterZipV2($subFolder);
        }

        return $studentData;
    }

    public function sendLetterInMailToStudentTrait($request, $validateStudent = true)
    {
        $letterStatusData = [];
        $letterStatusData['failData'] = [];

        if (is_array($request->student_id)) {
            $studentIds = $request->student_id;
        } else {
            $studentIds = (! empty($request->student_id)) ? explode(',', $request->student_id) : [];
        }
        $courseId = $request->input('course_id');
        $studentCourseId = $request->input('student_course_id');

        if (! empty($studentCourseId)) {
            $studentArrInfo = $this->studentProfileCommonRepository->getStudentCoursesById($studentCourseId, $studentIds);
        } else {
            $studentArrInfo = $this->studentProfileCommonRepository->getStudentCoursesByCourse($courseId, $studentIds);
        }

        $letterExistAttachmentIdArr = ($request->input('letter_existing_attachment_id') != '') ? explode(',', $request->input('letter_existing_attachment_id')) : [];
        $letterTemplateId = ($request->input('letter_template_id') != '') ? $request->input('letter_template_id') : '';

        $existAttachment = [];
        $existAttachmentLogData = [];
        if (count($letterExistAttachmentIdArr) > 0) {
            $existFilePath = Config::get('constants.uploadFilePath.LetterFile');
            $existDestinationPath = Helpers::changeRootPath($existFilePath, $letterTemplateId);
            $docList = $this->studentProfileCommonRepository->letterTemplateDocModel($letterExistAttachmentIdArr);
            foreach ($docList as $doc) {
                $existAttachment[] = $existDestinationPath['default'].$doc['letter_attachment'];
                $existAttachmentLogData[$doc['letter_attachment']] = $existDestinationPath['view'].$doc['letter_attachment'];
            }
        }

        $letterSubjectTxt = ($request->letter_subject != '') ? $request->letter_subject : 'Generate Student Letter';

        $sendCount = 0;
        $notSendCount = 0;
        $filePath = Config::get('constants.uploadFilePath.StudentLetterSetting');
        $destinationPath = Helpers::changeRootPath($filePath);
        File::makeDirectory($destinationPath['default'], 0777, true, true);
        $smtpData = SmtpSetup::get()->first();

        $failReason = '';
        $checkMailListener = false;
        foreach ($studentArrInfo as $studRow) {
            $flag = false;
            foreach ($studRow->studentcourses as $scInfo) {
                if ($courseId == $scInfo->course_id || $studentCourseId == $scInfo->id) {
                    if (! $checkMailListener) {
                        $failReason = $this->checkTestMail();
                        $checkMailListener = true;
                    }

                    if ($checkMailListener && empty($failReason)) {
                        [$convertedSubject, $convertedData] = $this->getLetterContentWithSubject($scInfo->student_id, $scInfo->course_id, $request->letter_content, $letterSubjectTxt);

                        $pdf = App::make('dompdf.wrapper');
                        $pdf->loadView('frontend.student.students-send-letter-pdf', ['template_type' => 1, 'letterBody' => $convertedData]);
                        $tempFileName = time().'.pdf';
                        $tempAttachment = $destinationPath['default'].$tempFileName;
                        $pdf->save($tempAttachment);

                        $existAttachmentLogData[$tempFileName] = $destinationPath['view'].$tempFileName;

                        $mailData = [
                            'id' => $scInfo->student_id,
                            'from' => $smtpData ? $smtpData->username : '',
                            'to' => $studRow->email,
                            'cc' => '',
                            'bcc' => '',
                            'subject' => $convertedSubject,
                            'attachFile' => array_merge($existAttachment, [$tempAttachment]),
                            'body' => $convertedData,
                            'attachmentLogData' => $existAttachmentLogData,
                        ];
                        $sendCount++;

                        event(new \App\Events\SendStudentMailEvent($mailData, $request));
                    } else {
                        $notSendCount++;
                        $letterStatusData['data']['failData'][] = $this->getFailDataArr($studRow, $failReason);
                    }
                    $flag = true;
                    break;
                }
            }
            if (! $flag) {
                $notSendCount++;
                $letterStatusData['data']['failData'][] = $this->getFailDataArr($studRow, "Course not found for $studRow->first_name");
            }
        }
        $letterStatusData['data']['success_msg'] = $sendCount.(($sendCount > 1) ? ' students' : ' student ').'were sent letter successfully.';
        $letterStatusData['data']['fail_msg'] = ($notSendCount > 0) ? ($notSendCount.(($notSendCount > 1) ? ' students ' : ' student ').'were not sent letter.') : '';

        return $letterStatusData;
    }

    public function studentIssueLetterZipV2($subFolder = null)
    {
        if (! $subFolder) {
            return false;
        }

        $zip = new ZipArchive;
        // get the name of zip file that will be created
        $file_name = "{$subFolder}.zip";

        $filePath = Config::get('constants.uploadFilePath.StudentLetterSetting');
        $fileDestinationPath = Helpers::changeRootPath($filePath, $subFolder)['default'];

        $zipPath = Config::get('constants.uploadFilePath.StudentLetterZip');

        $zipDestinationPath = Helpers::changeRootPath($zipPath)['default'];
        $zipPathUrl = Helpers::changeRootPath($zipPath)['view'];
        File::makeDirectory($zipDestinationPath, 0777, true, true);
        if ($zip->open($zipDestinationPath.$file_name, ZipArchive::CREATE) == true) {
            $files = File::files($fileDestinationPath);
            if (is_array($files) && count($files) > 0) {
                foreach ($files as $key => $value) {
                    $relativeName = basename($value);
                    $zip->addFile($value, $relativeName);
                }

                $zip->close();

                foreach ($files as $file) {
                    if (is_file($file)) {
                        unlink($file);
                    }
                }

                // Try to remove the directory if it is empty
                if (File::isDirectory($fileDestinationPath)) {
                    File::deleteDirectory($fileDestinationPath);
                }
            }
        }

        return $zipPathUrl.$file_name;
    }

    public function replaceContent($content, $objCollegeDetails, $college_logo, $arrState)
    {
        if (! empty($objCollegeDetails)) {

            $collegeInfo = CollegeDetails::query()->where('college_id', $objCollegeDetails['id'])->first()->toArray();
            $dataArr = [
                '{college_name}' => empty($objCollegeDetails['college_name']) ? null : $objCollegeDetails['college_name'],
                '{college_legal_name}' => empty($objCollegeDetails['legal_name']) ? null : $objCollegeDetails['legal_name'],
                '{college_ABN}' => empty($objCollegeDetails['ABN']) ? null : $objCollegeDetails['college_name'],
                '{college_CRICOS_code}' => $objCollegeDetails['CRICOS_code'],
                '{college_RTO_code}' => $objCollegeDetails['RTO_code'],
                '{college_street_address}' => $collegeInfo['street_address'],
                '{college_street_suburb}' => $collegeInfo['street_suburb'],
                '{college_street_state}' => (array_key_exists($collegeInfo['street_state'], $arrState)) ? $arrState[$collegeInfo['street_state']] : '',
                '{college_street_postcode}' => $collegeInfo['street_postcode'],
                '{college_contact_phone}' => $objCollegeDetails['contact_phone'],
                '{college_contact_email}' => $objCollegeDetails['contact_email'],
                '{college_account_email}' => $objCollegeDetails['account_email'],
                '{college_logo}' => $college_logo,
                '{college_url}' => $objCollegeDetails['college_url'],
            ];

            foreach ($dataArr as $key => $value) {
                $content = str_replace("$key", $value, $content);
            }

            return $content;
        } else {
            return false;
        }
    }

    public function getLetterContentWithSubject($studentId, $courseId, $content, $subject = '')
    {
        $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
        $destinationPath = Helpers::changeRootPath($filePath);
        $arrStudentCourse = $this->studentProfileCommonRepository->getStudentCoursesEmailContent($studentId, $courseId);
        $arrStudentEnrolledCourse = $this->studentProfileCommonRepository->getArrayStudentEnrolledCourseName($studentId);
        $arrStudentOfferedCourse = $this->studentProfileCommonRepository->getArrayStudentOfferedCourseName($studentId);
        if (! empty($arrStudentCourse)) {
            $filterArray = [
                'student_id' => $studentId,
                'course_id' => $courseId,
            ];

            $row = $arrStudentCourse[0];
            $row['arrStudentEnrolledCourse'] = $arrStudentEnrolledCourse;
            $row['arrStudentOfferedCourse'] = $arrStudentOfferedCourse;
            $row['arrStudentEnrolledUnit'] = StudentSubjectEnrolment::with(['unit'])->where($filterArray)->get()->toArray();

            $imgArr = [
                'collegeLogoUrl' => $this->getCollegeAssetUrl($destinationPath['view'], $row['college_logo']),
                'collegeSignatureUrl' => $this->getCollegeAssetUrl($destinationPath['view'], $row['college_signature']),
                'deanSignatureUrl' => $this->getCollegeAssetUrl($destinationPath['view'], $row['dean_signature']),
            ];
            $dataArr = $this->letterDataBind($row, $imgArr);
            foreach ($dataArr as $key => $value) {
                $content = str_replace("$key", $value, $content);
                $subject = str_replace("$key", $value, $subject);
            }

            return [$subject, $content];
        } else {
            return false;
        }
    }

    public function letterDataBind($row, $imgArr, $courseId = '')
    {
        $validCourse = (! empty($courseId)) ? true : false;

        return [
            '{AlterEmail1}' => $row['emergency_email'],
            '{AlterEmail2}' => $row['emergency_email'],
            '{CollegeLogo}' => $this->generateCollegeAsset($imgArr['collegeLogoUrl'], 'College Logo'),
            '{CollegeEmail}' => $row['college_email'],
            '{Country}' => $row['country_name'],
            '{CountryBirth}' => $row['birth_country'],
            '{CurrentDate}' => date('d-m-Y'),
            '{DoB}' => date('d-m-Y', strtotime($row['birth_date'])),
            '{DOB}' => date('d-m-Y', strtotime($row['birth_date'])),
            '{DoB Without Stroke}' => '******************',
            '{Email}' => $row['student_email'],
            '{ExpDate}' => date('d-m-Y', strtotime($row['visa_expiry_date'])),
            '{Fax}' => $row['fax'],
            '{Student ID}' => $row['generated_stud_id'],
            '{Student Name}' => $row['first_name'].' '.$row['family_name'],
            '{StudentId}' => $row['generated_stud_id'],
            '{FirstName}' => $row['first_name'],
            '{MiddleName}' => $row['middel_name'],
            '{LastName}' => $row['family_name'],
            '{Gender}' => $row['gender'],
            '{Mobile}' => $row['current_mobile_phone'],
            '{Nationality}' => $row['nationality'],
            '{NickName}' => $row['nickname'],
            '{PassportNo}' => $row['passport_no'],
            '{Phone}' => $row['current_mobile_phone'],
            '{Postcode}' => $row['current_postcode'],
            '{State}' => $row['current_state'],
            '{StreetAddress}' => $row['current_street_name'],
            '{StreetNumber}' => $row['current_street_no'],
            '{UnitDetail}' => @$row['enrolledUnitList'],
            '{BuildingName}' => $row['current_building_name'],
            '{Suburb}' => $row['current_city'],
            '{Title}' => $row['name_title'],
            '{UserName}' => $row['generated_stud_id'],
            '{VisaType}' => $row['visa_type'],
            '{CourseCode}' => $validCourse ? $row['course_code'] : '{CourseCode}',
            '{CourseName}' => $validCourse ? $row['course_name'] : '{CourseName}',
            '{CollegeRtoCode}' => $row['RTO_code'],
            '{CollegeCircosCode}' => $row['CRICOS_code'],
            '{CollegeLegalName}' => $row['legal_name'],
            '{CollegeName}' => $row['entity_name'],
            '{CollegeSignature}' => $this->generateCollegeAsset($imgArr['collegeSignatureUrl'], 'College Signature'),
            '{DeanName}' => $row['dean_name'],
            '{DeanSignature}' => $this->generateCollegeAsset($imgArr['deanSignatureUrl'], 'Dean Signature'),
            '{CollegeContactPerson}' => $row['contact_person'],
            '{CollegeContactPhone}' => $row['contact_phone'],
            '{CollegeURL}' => $row['college_url'],
            '{CollegeABN}' => $row['college_ABN'],
            '{CollegeFax}' => $row['fax'],
            '{CourseType}' => $validCourse ? $row['course_type'] : '{CourseType}',
            '{Campus}' => $validCourse ? $row['campus_name'] : '{Campus}',
            '{StudentType}' => $row['student_type'],
            '{TeacherFirstName}' => $row['teacher_first_name'],
            '{TeacherLastName}' => $row['teacher_last_name'],
            '{TeacherEmail}' => $row['teacher_email'],
            '{TeacherMobile}' => $row['teacher_mobile'],
            '{AgencyName}' => $validCourse ? $row['agency_name'] : '{AgencyName}',
            '{AgentName}' => $validCourse ? $row['agent_name'] : '{AgentName}',
            '{AgentEmail}' => $validCourse ? $row['agent_email'] : '{AgentEmail}',
            '{AgentTelephone}' => $validCourse ? $row['agent_telephone'] : '{AgentTelephone}',
            '{EnrolledCourseList}' => count($row['arrStudentEnrolledCourse']) > 0 ? $this->generateCourseList($row['arrStudentEnrolledCourse']) : 'Course not found in enrolled state',
            '{OfferedCourseList}' => count($row['arrStudentOfferedCourse']) > 0 ? $this->generateCourseList($row['arrStudentOfferedCourse']) : 'Course not found in offered state',
            '{CourseStartDate}' => $validCourse ? date('d-m-Y', strtotime($row['start_date'])) : '{CourseStartDate}',
            '{CourseEndDate}' => $validCourse ? date('d-m-Y', strtotime($row['finish_date'])) : '{CourseEndDate}',
            '{CourseDuration}' => $validCourse ? $row['total_weeks'].' Weeks' : '{CourseDuration}',
            '{StudentContactEmail}' => $row['personalEmail'],
            '{StudentAlternateEmail}' => $row['AlternateEmail'],
            '{StudentEmergencyEmail}' => $row['emergency_email'],
            '{EnrolledCourseListDesign2}' => (count($row['arrStudentEnrolledCourse']) > 0) ? $this->generateCourseListDesign2($row['arrStudentEnrolledCourse']) : '{EnrolledCourseListDesign2}',
            '{EnrolledUnitList}' => (count($row['arrStudentEnrolledUnit']) > 0) ? $this->generateEnrolledUnitList($row['arrStudentEnrolledUnit']) : '{EnrolledUnitList}',
            '{Current Date}' => date('d-m-Y'), // Old college have space in this tag
            '{Dob}' => date('d-m-Y', strtotime($row['birth_date'])), // B is small for that college
            '{Lastname}' => $row['family_name'], // N is small
            '{OfferNo}' => $validCourse ? $row['offer_id'] : '{OfferNo}',
            '{OfferId}' => $validCourse ? $row['offer_id'] : '{OfferId}',
            '{Passportno}' => $row['passport_no'],
            '{CampusName}' => $validCourse ? $row['campus_name'] : '{CampusName}',
            '{PostCode}' => $row['current_postcode'],
        ];
    }

    private function generateCollegeAsset($imgURL, $imgAlt = 'image')
    {
        // return `<img src='$imgURL' alt='$imgAlt' style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;" />`;
        $data = [
            'imgURL' => $imgURL,
            'imgAlt' => $imgAlt,
        ];

        return view('v2.templatepartials.logo', $data)->render();
    }

    private function getCollegeAssetUrl($basePath, $logo)
    {
        $domain = url('/');

        // return $domain . str_replace('\\', "/", $basePath) . $logo;
        return UploadService::imageEmbed($basePath.$logo);
    }

    private function generateCourseList($courses)
    {
        return view('v2.templatepartials.course-list-design-1', ['courses' => $courses])->render();
    }

    private function generateCourseListDesign2($courses)
    {
        return view('v2.templatepartials.course-list-design-2', ['courses' => $courses])->render();
    }

    private function generateEnrolledUnitList($units)
    {
        return view('v2.templatepartials.enrolled-unit', ['units' => $units])->render();
    }

    public function studentsWarningLetterActionsTrait(array $request, $action = '')
    {
        /* get all the request parameters */
        $studentIds = $request['student_id'] ?? [];
        $course_id = $request['course_id'] ?? 0;
        $batch_id = $request['batch_id'] ?? 0;
        $campus_id = $request['campus_id'] ?? 0;
        $letter_content = $request['letter_content'] ?? '';
        $warningInitiateBatchDate = Carbon::now();
        /* letter subject to be requested from template */
        // get the storage full path and relative path for the files to store
        $filePath = Config::get('constants.uploadFilePath.StudentLetterSetting');

        $subFolder = $this->getUniqueFileName('StudentLetters_');

        $destinationPath = Helpers::changeRootPath($filePath, $subFolder);

        File::makeDirectory($destinationPath['default'], 0777, true, true);

        // get the lettersetting for watermarking the letter
        // applies if action is letterwithwatermark
        $letterSettingData = LetterSetting::find(1);
        // get the storage full path and relative path for the watermarked files to store
        $filePath1 = Config::get('constants.uploadFilePath.LetterSetting');
        $destinationLetterPath = Helpers::changeRootPath($filePath1);
        $watermarkPath = $destinationLetterPath['view'];

        /*
        get attachment that is provided in the letter template
        //generally applies to the letters when to be sent (when action is send)
        */
        $letterExistAttachmentId = $request['letter_attachments'] ?? [];
        $letterExistAttachmentIdArr = (! empty($letterExistAttachmentId))
                                        ? (! is_array($letterExistAttachmentId) ? explode(',', $letterExistAttachmentId) : $letterExistAttachmentId)
                                        : [];
        $letterTemplateId = $request['template_id'] ?? null;
        $letterTemplateTitle = $request['letter_title'] ?? '';
        $letterSubject = $request['letter_subject'] ?? '';

        $letterSubjectTxt = empty($letterSubject) ? 'Generate Student Letter' : $letterSubject;

        $existAttachment = [];
        $existAttachmentLogData = [];
        if (count($letterExistAttachmentIdArr) > 0) {
            $existFilePath = Config::get('constants.uploadFilePath.LetterFile');
            $existDestinationPath = Helpers::changeRootPath($existFilePath, $letterTemplateId);
            $docList = $this->studentProfileCommonRepository->letterTemplateDocModel($letterExistAttachmentIdArr);
            foreach ($docList as $doc) {
                $existAttachment[] = $existDestinationPath['default'].$doc['letter_attachment'];
                $existAttachmentLogData[$doc['letter_attachment']] = $existDestinationPath['view'].$doc['letter_attachment'];
            }
        }
        /* get colleget details */
        $objCollegeDetails = CollegeDetails::where('rto_college_details.college_id', Auth::user()->college_id)
            ->join('rto_colleges', 'rto_colleges.id', '=', 'rto_college_details.college_id')
            ->get();
        // get all student information
        $studentsDetails = Student::whereIn('id', $studentIds)->get();
        $sendCount = 0;
        $generatedLetters = [];
        foreach ($studentsDetails as $studRow) {
            /* common */
            [$convertedSubject, $convertedData] = $this->getLetterContentWithSubject($studRow->id, null, $letter_content, $letterSubjectTxt);
            $contentData['data'] = $convertedData;
            $contentData['subject'] = $convertedSubject;
            $data['letterBody'] = $convertedData;
            $data['template_type'] = $contentData['data'];
            if ($action == 'letterwithwatermark') {
                /* watermark */
                $data['watermarkPath'] = $watermarkPath;
                $data['letterSettingData'] = $letterSettingData;
                $data['template_type'] = 1;
                // for watermark
                $arrState = Config::get('constants.arrState');
                $filePath1 = Config::get('constants.uploadFilePath.CollegeLogo');
                $destinationPath1 = Helpers::changeRootPath($filePath1);
                $logoPath = $destinationPath1['view'];
                $arrStudentInfo = $this->student->getStudentDetail($studRow->id, Config::get('constants.arrRoleType'));
                $data['clg_logo'] = $clgLogo = $logoPath.$arrStudentInfo[0]->college_logo;
                $data['latterHead'] = $this->replaceContent($letterSettingData->header, $objCollegeDetails[0], $clgLogo, $arrState);
                $data['latterFooter'] = $this->replaceContent($letterSettingData->footer, $objCollegeDetails[0], $clgLogo, $arrState);
                /* watermark end */
            }
            /* common */
            $pdf = App::make('dompdf.wrapper');
            $pdf->loadView('frontend.student.students-send-letter-pdf', $data);
            $fileName = implode('-', ['Attendance', 'warning', $studRow->first_name, $studRow->generated_stud_id, Carbon::today()->format('Y-m-d')]);
            // $fileName =  time() . '.' . 'pdf';
            $tempAttachment = $destinationPath['default'].$fileName.'.pdf';
            $pdf->save($tempAttachment);
            $generatedLetters[] = $tempAttachment;
            /* common end */
            if ($action == 'send') {
                /* to send the email */
                $existAttachmentLogData[$studRow->generated_stud_id] = $destinationPath['view'].$fileName;
                $mailData = [
                    'id' => $studRow->id,
                    'to' => $studRow->email,
                    'cc' => '',
                    'bcc' => '',
                    'subject' => $convertedSubject,
                    'attachFile' => array_merge($existAttachment, [$tempAttachment]),
                    'body' => $convertedData,
                    'attachmentLogData' => $existAttachmentLogData,
                ];
                $sendCount++;
                /*
                An event is dispatched when a new warning log is inserted
                The event listner will send the email for the log and the warning communication log is also kept together
                */
                $this->keeWarningLog($mailData, $request, $warningInitiateBatchDate);

                // $request["is_attendance_warning"] = 1;
                // event(new \App\Events\SendStudentMailEvent($mailData, $request));
            }
            /* to send the email ends */
        }

        if ($sendCount > 0) {
            $studentData['data']['success_msg'] = config_locale(
                [
                    'messages.attendance.letter_generated',
                    [
                        'count' => $sendCount,
                        'label' => 'student'.($sendCount > 1 ? 's' : ''),
                    ],
                ]);
            $studentData['data']['file_name'] = $this->studentIssueLetterZipV2($subFolder);
        } else {
            $studentData['data']['fail_msg'] = 'Letter was not sent';
        }

        return $studentData;
    }

    public function keeWarningLog($mailData, $request, $warningInitiateBatchDate)
    {
        return StudentAttendanceWarnings::create([
            'student_id' => $mailData['id'] ?? 0,
            'timetable_id' => $request['batch_id'],
            'type' => 'manual',
            'mail_log_id' => null,
            'initiated_on' => $warningInitiateBatchDate,
            'days_count' => 1,
            'student_in_contact' => 0,
            'intervention_needed' => 0,
            'severity_level' => 1,
            'mail_data' => json_encode($mailData),
            'created_by' => Auth::user()->id,
        ]);
    }
}
